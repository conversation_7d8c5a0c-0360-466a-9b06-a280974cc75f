/* src/index.css */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@300;400;600&display=swap');

:root {
  --primary: #1a6b8a;
  --secondary: #2d9a6e;
  --accent: #ff6b6b;
  --dark: #2c3e50;
  --light: #f8f9fa;
  --ocean: #1a6b8a;
  --forest: #27ae60;
  --earth: #d35400;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  color: var(--dark);
  background-color: #f0f7ff;
  line-height: 1.6;
}

h1, h2, h3, h4, h5 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  color: var(--dark);
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Header & Navigation */
header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 50px;
  margin-right: 15px;
}

.logo-text {
  font-size: 28px;
  font-weight: 700;
  color: white;
  font-family: 'Montserrat', sans-serif;
}

.logo-text span {
  color: #ffd166;
}

.nav-links {
  display: flex;
  list-style: none;
}

.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 0 10px;
}

.nav-links a:hover {
  background: rgba(255, 255, 255, 0.15);
}

.btn {
  display: inline-block;
  background: var(--accent);
  color: white;
  padding: 12px 28px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 14px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2px solid white;
  box-shadow: none;
}

.btn-outline:hover {
  background: white;
  color: var(--primary);
}

/* Hero Section */
.hero {
  background: linear-gradient(rgba(26, 107, 138, 0.85), rgba(45, 154, 110, 0.85)), url('https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3') center/cover no-repeat;
  color: white;
  padding: 120px 0 100px;
  text-align: center;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  color: white;
  line-height: 1.2;
}

.hero p {
  font-size: 1.5rem;
  max-width: 800px;
  margin: 0 auto 40px;
  font-weight: 300;
}

.hero-btns {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

/* Features Section */
.features {
  padding: 100px 0;
  background: white;
}

.section-title {
  text-align: center;
  margin-bottom: 60px;
}

.section-title h2 {
  font-size: 2.5rem;
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.section-title h2:after {
  content: '';
  position: absolute;
  width: 80px;
  height: 4px;
  background: var(--secondary);
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

.section-title p {
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.1rem;
  color: #555;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid var(--secondary);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: var(--primary);
  background: rgba(26, 107, 138, 0.05);
}

.feature-content {
  padding: 25px;
}

.feature-content h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--primary);
}

/* Dashboard Preview */
.dashboard {
  padding: 100px 0;
  background: linear-gradient(to bottom, #e8f4fc, #f0f7ff);
}

.dashboard-container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dashboard-header {
  background: var(--dark);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-tabs {
  display: flex;
  list-style: none;
  flex-wrap: wrap;
  margin-top: 10px;
}

.dashboard-tabs li {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 15px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
}

.dashboard-tabs li:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dashboard-tabs li.active {
  background: var(--accent);
}

.dashboard-content {
  padding: 30px;
  min-height: 400px;
  display: flex;
  flex-wrap: wrap;
}

.map-container {
  flex: 1;
  min-width: 300px;
  height: 350px;
  background: #e0f0ff;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  background-image: url('https://images.unsplash.com/photo-1593118247619-e2d6f056869e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');
  background-size: cover;
  background-position: center;
}

.map-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 15px;
  border-radius: 8px;
  width: 250px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stats-container {
  width: 300px;
  padding-left: 30px;
}

.stat-card {
  background: white;
  border-left: 4px solid var(--secondary);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 0 8px 8px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.stat-card h4 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: var(--primary);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--secondary);
}

/* Impact Section */
.impact {
  padding: 100px 0;
  background: white;
}

.sdg-cards {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
  margin-top: 50px;
}

.sdg-card {
  width: 300px;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  text-align: center;
  transition: all 0.3s ease;
}

.sdg-card:hover {
  transform: scale(1.05);
}

.sdg-header {
  padding: 25px 20px;
  color: white;
  font-weight: 700;
}

.sdg-13 .sdg-header { background: #e5243b; }
.sdg-14 .sdg-header { background: #0a97d9; }
.sdg-15 .sdg-header { background: #56c02b; }

.sdg-body {
  padding: 25px 20px;
}

.sdg-body h3 {
  margin-bottom: 15px;
  color: var(--dark);
}

.progress {
  height: 25px;
  background: #e9ecef;
  border-radius: 20px;
  margin-top: 20px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--secondary);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
  transition: width 1.5s ease-in-out;
}

/* CTA Section */
.cta {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  text-align: center;
}

.cta h2 {
  font-size: 2.8rem;
  margin-bottom: 20px;
  color: white;
}

.cta p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto 40px;
  font-weight: 300;
}

.btn-light {
  background: white;
  color: var(--primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-light:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.cta .btn {
  margin: 0 10px;
}

/* Footer */
footer {
  background: var(--dark);
  color: white;
  padding: 70px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 50px;
}

.footer-logo {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
}

.footer-logo span {
  color: #ffd166;
}

.footer-about p {
  margin-bottom: 20px;
  color: #bbb;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--accent);
  transform: translateY(-3px);
}

.footer-heading {
  font-size: 1.3rem;
  margin-bottom: 25px;
  position: relative;
}

.footer-heading:after {
  content: '';
  position: absolute;
  width: 40px;
  height: 3px;
  background: var(--accent);
  bottom: -10px;
  left: 0;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.footer-links a {
  color: #bbb;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent);
  padding-left: 5px;
}

.footer-links i {
  margin-right: 10px;
  width: 20px;
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #999;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 992px) {
  .hero h1 {
    font-size: 2.8rem;
  }
  
  .dashboard-content {
    flex-direction: column;
  }
  
  .stats-container {
    width: 100%;
    padding-left: 0;
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .stat-card {
    flex: 1;
    min-width: 200px;
  }
  
  .hero-btns {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-btns .btn {
    width: 100%;
    max-width: 300px;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 80px;
    right: -100%;
    flex-direction: column;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    width: 100%;
    padding: 20px;
    transition: all 0.5s ease;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  
  .nav-links.active {
    right: 0;
  }
  
  .nav-links a {
    display: block;
    margin: 10px 0;
    padding: 15px;
    text-align: center;
  }
  
  .mobile-menu-icon {
    display: block;
    color: white;
    font-size: 24px;
    cursor: pointer;
  }
  
  .hero h1 {
    font-size: 2.3rem;
  }
  
  .hero p {
    font-size: 1.2rem;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .feature-card {
    min-width: 100%;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dashboard-tabs {
    margin-top: 20px;
  }
  
  .sdg-card {
    width: 100%;
    max-width: 400px;
  }
  
  .cta .btn {
    width: 100%;
    max-width: 300px;
    margin: 10px 0;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 1.8rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .section-title h2 {
    font-size: 1.8rem;
  }
  
  .stat-card {
    min-width: 100%;
  }
}