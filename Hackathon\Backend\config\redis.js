// const redis=require('redis');

// const redisClient=redis.createClient({
//     username: 'default',
//     password: 'ZOoYW0ijp7LUREakBDzuCYbn8sPLmgTc',
//     socket: {
//         host: 'redis-10600.crce206.ap-south-1-1.ec2.redns.redis-cloud.com',
//         port: 10600
//     }
// })
// const connectReddis=async()=>{

//     await redisClient.connect();
//     console.log("Connected to Reddis");
// }
// connectReddis();

// module.exports=redisClient;