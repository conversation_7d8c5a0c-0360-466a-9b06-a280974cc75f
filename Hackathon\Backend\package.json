{"name": "ecoharmony", "version": "1.0.0", "description": "Backend for EcoHarmony environmental platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "nodemailer": "^6.9.4", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}}