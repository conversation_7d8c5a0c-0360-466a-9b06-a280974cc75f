// const bcrypt=require("bcrypt")

// // Hashing SHA256 we use for storing the password but not always
// // Rainbow Table 
// // Hmlog hashing table mein salt add krte just like add some strings
        


// const password="Rohit@123";

// async function Hashing(){

// const hashpass= await bcrypt.hash(password,10);

// const ans=await bcrypt.compare(password,hashpass);
// console.log(ans);

// }

// Hashing();