{"name": "project1", "version": "1.0.0", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "parcel build index.html", "dev": "parcel index.html"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "cookie-parser": "^1.4.7", "dotenv": "^17.2.1", "express": "^5.1.0", "express-middleware": "^3.4.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.2", "parcel": "^2.15.4", "ratelimiter": "^3.4.1", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-leaflet": "^5.0.0", "react-router-dom": "^7.8.0", "redis": "^5.8.2", "tailwindcss": "^4.1.11", "validator": "^13.15.15"}}